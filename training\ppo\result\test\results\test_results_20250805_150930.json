{"test_info": {"timestamp": "2025-08-05T15:09:30.873837", "model_path": "result\\train\\models\\seed_79811_2025_0805_142135\\best_model.pth", "total_episodes": 10, "test_duration": 7.762412071228027, "env_id": "parking-v0", "env_name": "custom_parking", "agent": "PPO_Clip", "seed": null}, "statistics": {"mean_score": -2.4441571993069373, "std_score": 1.1794367379137445, "max_score": -0.20354483612754848, "min_score": -4.233366556465626, "median_score": -2.673532306216657, "success_rate": 0.0, "success_count": 0, "mean_episode_length": 21.4, "std_episode_length": 6.590902821313633, "max_episode_length": 40, "min_episode_length": 16}, "failure_analysis": {"撞墙": 8}, "episode_details": [{"episode": 1, "score": -3.39301146985963, "length": 40, "success": false, "failure_reason": "撞墙", "info": {"speed": 7.815699041883154, "crashed": true, "action": [-0.06694784760475159, 0.6736200451850891], "is_success": false, "path_distance": 3.812044342204872, "path_index": 2, "path_following_reward": 0.08563866973385385, "action_change": 0.0, "smoothness_reward": -0.07283440542221069, "is_smooth_action": true, "reward_breakdown": {"base_reward": -5.150181514051032, "path_reward": 0.08563866973385385, "smoothness_reward": -0.07283440542221069, "total_reward": -1.2795053988141736}, "original_action": [-0.0818057730793953, 0.889185905456543], "smoothed_action": [-0.06694784760475159, 0.6736200451850891], "action_smoothing_applied": true, "episode_step": 40, "episode_score": -3.393011514328239, "reset_obs": [0.0, 0.0, 0.0, -0.0, 0.28561967611312866, -0.9583430290222168, 0.10000000149011612, 0.14000000059604645, 0.0, 0.0, 6.123234262925839e-17, 1.0, 0.0, 0.0, 0.0, -0.0, 0.28561967611312866, -0.9583430290222168]}}, {"episode": 2, "score": -0.7851646542549133, "length": 21, "success": true, "failure_reason": null, "info": {"speed": 10.751759089529514, "crashed": false, "action": [0.3819970488548279, 0.6748172044754028], "is_success": true, "path_distance": 0.8704019810490655, "path_index": 2, "path_following_reward": 0.17388794056852805, "action_change": 0.0, "smoothness_reward": -0.022978064537048336, "is_smooth_action": true, "reward_breakdown": {"base_reward": -0.11039538809050382, "path_reward": 0.17388794056852805, "smoothness_reward": -0.022978064537048336, "total_reward": 0.053600607127375986}, "original_action": [0.3466031849384308, 0.6543785929679871], "smoothed_action": [0.3819970488548279, 0.6748172044754028], "action_smoothing_applied": true, "episode_step": 21, "episode_score": -0.7851646636492511, "reset_obs": [0.0, 0.0, -0.0, 0.0, -0.14259083569049835, 0.9897817373275757, 0.019999999552965164, 0.14000000059604645, 0.0, 0.0, 6.123234262925839e-17, 1.0, 0.0, 0.0, -0.0, 0.0, -0.14259083569049835, 0.9897817373275757]}}, {"episode": 3, "score": -3.33494824974332, "length": 24, "success": false, "failure_reason": "撞墙", "info": {"speed": 13.430320963263517, "crashed": true, "action": [0.23337821662425995, -0.28222936391830444], "is_success": false, "path_distance": 4.003758631109087, "path_index": 2, "path_following_reward": -0.02003758631109087, "action_change": 0.0, "smoothness_reward": 0.02013247632980347, "is_smooth_action": true, "reward_breakdown": {"base_reward": -5.171009889654011, "path_reward": -0.02003758631109087, "smoothness_reward": 0.02013247632980347, "total_reward": -1.298738146486597}, "original_action": [0.22614195942878723, -0.17523756623268127], "smoothed_action": [0.23337821662425995, -0.28222936391830444], "action_smoothing_applied": true, "episode_step": 24, "episode_score": -3.3349482823430625, "reset_obs": [0.0, 0.0, 0.0, 0.0, 0.9519133567810059, 0.3063673675060272, -0.18000000715255737, 0.14000000059604645, 0.0, 0.0, 6.123234262925839e-17, 1.0, 0.0, 0.0, 0.0, 0.0, 0.9519133567810059, 0.3063673675060272]}}, {"episode": 4, "score": -3.035598380956799, "length": 22, "success": false, "failure_reason": "撞墙", "info": {"speed": -13.000014206568402, "crashed": true, "action": [-0.5327576994895935, -0.01601467654109001], "is_success": false, "path_distance": 3.982897158259696, "path_index": 2, "path_following_reward": 0.08051308525220914, "action_change": 0.0, "smoothness_reward": 0.026, "is_smooth_action": true, "reward_breakdown": {"base_reward": -5.2584993967076255, "path_reward": 0.08051308525220914, "smoothness_reward": 0.026, "total_reward": -1.2688683065508017}, "original_action": [-0.5150138139724731, 0.035183269530534744], "smoothed_action": [-0.5327576994895935, -0.01601467654109001], "action_smoothing_applied": true, "episode_step": 22, "episode_score": -3.0355983563734243, "reset_obs": [0.0, 0.0, 0.0, 0.0, 0.21731971204280853, 0.976100504398346, 0.019999999552965164, 0.14000000059604645, 0.0, 0.0, 6.123234262925839e-17, 1.0, 0.0, 0.0, 0.0, 0.0, 0.21731971204280853, 0.976100504398346]}}, {"episode": 5, "score": -2.3114662314765155, "length": 18, "success": false, "failure_reason": "撞墙", "info": {"speed": 12.79504916270574, "crashed": true, "action": [0.4493483304977417, 0.14449773728847504], "is_success": false, "path_distance": 4.597507507324804, "path_index": 2, "path_following_reward": -0.02597507507324804, "action_change": 0.0, "smoothness_reward": 0.046, "is_smooth_action": true, "reward_breakdown": {"base_reward": -5.191726290431542, "path_reward": -0.02597507507324804, "smoothness_reward": 0.046, "total_reward": -1.3235756414784328}, "original_action": [0.3459100127220154, -0.11261148750782013], "smoothed_action": [0.4493483304977417, 0.14449773728847504], "action_smoothing_applied": true, "episode_step": 18, "episode_score": -2.311466256237197, "reset_obs": [0.0, 0.0, -0.0, 0.0, -0.20103149116039276, 0.9795847535133362, 0.05999999865889549, -0.14000000059604645, 0.0, 0.0, 6.123234262925839e-17, -1.0, 0.0, 0.0, -0.0, 0.0, -0.20103149116039276, 0.9795847535133362]}}, {"episode": 6, "score": -4.233366556465626, "length": 18, "success": false, "failure_reason": "撞墙", "info": {"speed": -16.81229418516159, "crashed": true, "action": [-0.9932833909988403, 0.022543922066688538], "is_success": false, "path_distance": 23.091726493539362, "path_index": 0, "path_following_reward": -0.1, "action_change": 0.0, "smoothness_reward": -0.038388013839721666, "is_smooth_action": true, "reward_breakdown": {"base_reward": -5.547732959747782, "path_reward": -0.1, "smoothness_reward": -0.038388013839721666, "total_reward": -1.4505302433968759}, "original_action": [-1.0715203285217285, 0.1844336986541748], "smoothed_action": [-0.9932833909988403, 0.022543922066688538], "action_smoothing_applied": true, "episode_step": 18, "episode_score": -4.2333665145862955, "reset_obs": [0.0, 0.0, -0.0, 0.0, -0.6976519823074341, 0.7164368033409119, -0.25999999046325684, -0.14000000059604645, 0.0, 0.0, 6.123234262925839e-17, -1.0, 0.0, 0.0, -0.0, 0.0, -0.6976519823074341, 0.7164368033409119]}}, {"episode": 7, "score": -1.911762214062037, "length": 16, "success": false, "failure_reason": "撞墙", "info": {"speed": -13.059213262134133, "crashed": true, "action": [-0.5159052610397339, 0.21001964807510376], "is_success": false, "path_distance": 4.031242790833025, "path_index": 2, "path_following_reward": -0.020312427908330245, "action_change": 0.0, "smoothness_reward": 0.016797642230987547, "is_smooth_action": true, "reward_breakdown": {"base_reward": -5.250008400387097, "path_reward": -0.020312427908330245, "smoothness_reward": 0.016797642230987547, "total_reward": -1.3224589034931924}, "original_action": [-0.4294005334377289, 0.22545400261878967], "smoothed_action": [-0.5159052610397339, 0.21001964807510376], "action_smoothing_applied": true, "episode_step": 16, "episode_score": -1.9117622498009976, "reset_obs": [0.0, 0.0, 0.0, 0.0, 0.9950255751609802, 0.0996195524930954, 0.14000000059604645, 0.14000000059604645, 0.0, 0.0, 6.123234262925839e-17, 1.0, 0.0, 0.0, 0.0, 0.0, 0.9950255751609802, 0.0996195524930954]}}, {"episode": 8, "score": -2.10019146441482, "length": 18, "success": false, "failure_reason": "撞墙", "info": {"speed": 12.965441746888338, "crashed": true, "action": [0.512891411781311, 0.6324385404586792], "is_success": false, "path_distance": 3.9194882091126373, "path_index": 2, "path_following_reward": 0.0824153537266209, "action_change": 0.0, "smoothness_reward": -0.049892624855041506, "is_smooth_action": true, "reward_breakdown": {"base_reward": -5.134803168179646, "path_reward": 0.0824153537266209, "smoothness_reward": -0.049892624855041506, "total_reward": -1.2589662713953613}, "original_action": [0.37018507719039917, 0.5104026794433594], "smoothed_action": [0.512891411781311, 0.6324385404586792], "action_smoothing_applied": true, "episode_step": 18, "episode_score": -2.1001914056381303, "reset_obs": [0.0, 0.0, -0.0, -0.0, -0.2525043487548828, -0.9675957560539246, -0.18000000715255737, -0.14000000059604645, 0.0, 0.0, 6.123234262925839e-17, -1.0, 0.0, 0.0, -0.0, -0.0, -0.2525043487548828, -0.9675957560539246]}}, {"episode": 9, "score": -0.20354483612754848, "length": 18, "success": true, "failure_reason": null, "info": {"speed": 9.810172036290158, "crashed": false, "action": [0.24529488384723663, 0.2914821207523346], "is_success": true, "path_distance": 0.7172852808126434, "path_index": 2, "path_following_reward": 0.1784814415756207, "action_change": 0.0, "smoothness_reward": 0.011022145509719847, "is_smooth_action": true, "reward_breakdown": {"base_reward": -0.09386254987300158, "path_reward": 0.1784814415756207, "smoothness_reward": 0.011022145509719847, "total_reward": 0.06453061969698992}, "original_action": [0.1731121838092804, 0.19133545458316803], "smoothed_action": [0.24529488384723663, 0.2914821207523346], "action_smoothing_applied": true, "episode_step": 18, "episode_score": -0.20354482961331238, "reset_obs": [0.0, 0.0, 0.0, -0.0, 0.30055493116378784, -0.9537644982337952, 0.18000000715255737, -0.14000000059604645, 0.0, 0.0, 6.123234262925839e-17, -1.0, 0.0, 0.0, 0.0, -0.0, 0.30055493116378784, -0.9537644982337952]}}, {"episode": 10, "score": -3.132517935708165, "length": 19, "success": false, "failure_reason": "撞墙", "info": {"speed": -11.291786074638363, "crashed": true, "action": [-0.44086718559265137, -0.2238556295633316], "is_success": false, "path_distance": 8.490363619605303, "path_index": 2, "path_following_reward": -0.1, "action_change": 0.0, "smoothness_reward": 0.027137324452400212, "is_smooth_action": true, "reward_breakdown": {"base_reward": -5.358605476789747, "path_reward": -0.1, "smoothness_reward": 0.027137324452400212, "total_reward": -1.4009495516551222}, "original_action": [-0.4985700249671936, -0.4413149952888489], "smoothed_action": [-0.44086718559265137, -0.2238556295633316], "action_smoothing_applied": true, "episode_step": 19, "episode_score": -3.1325178851536624, "reset_obs": [0.0, 0.0, 0.0, -0.0, 0.6973491311073303, -0.716731607913971, 0.25999999046325684, -0.14000000059604645, 0.0, 0.0, 6.123234262925839e-17, -1.0, 0.0, 0.0, 0.0, -0.0, 0.6973491311073303, -0.716731607913971]}}], "config": {"max_episode_steps": 150, "render": true, "render_mode": "human", "device": "cuda:0"}}