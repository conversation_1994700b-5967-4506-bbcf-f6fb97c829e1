# ============================================================================
# 在导入任何其他模块之前先设置警告过滤
# ============================================================================
import warnings
import os

# 禁用所有警告
warnings.filterwarnings('ignore')

# 特别是禁用 gymnasium 的警告
os.environ['GYMNASIUM_DISABLE_WARNINGS'] = 'True'

# 禁用特定的警告
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", message=".*already in registry.*")

import numpy as np
import gymnasium as gym
from HighwayEnv.highway_env.envs.parking_env import ParkingEnv
from typing import Optional, List, Tuple

# 类型别名
State = Tuple[float, float, float]  # (x, y, theta)
Path = List[State]
Motion = Tuple[float, float]  # (distance, steering_angle)
import heapq
import sys
import os
from scipy.spatial import ConvexHull


# 添加HybridAstarPlanner路径
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '../../../HybridAstarPlanner'))


# 移除专业版本导入，使用简化版本


class SimpleHybridAStar:
    """极简但有效的Hybrid A*路径规划器"""

    def __init__(self):
        # 简化参数
        self.step_size = 2.0
        self.wheelbase = 2.5

        # 改进的运动原语 - 增加更多转向角度
        self.motions = []

        # 前进运动：不同转向角度
        forward_distance = 1.5
        steering_angles = [0, 0.2, 0.4, 0.6, 0.8, -0.2, -0.4, -0.6, -0.8]  # 0到±45度
        for angle in steering_angles:
            self.motions.append((forward_distance, angle))

        # 后退运动：较少的转向角度
        backward_distance = -1.0
        backward_angles = [0, 0.3, -0.3, 0.5, -0.5]  # 后退时转向角度稍小
        for angle in backward_angles:
            self.motions.append((backward_distance, angle))

    def plan(self, start_state: State, goal_state: State, obstacles: List[dict]) -> Optional[Path]:
        """主路径规划接口：根据起点终点生成可行驾驶轨迹"""

        # 策略1：尝试基于车辆运动学的智能路径生成
        reasonable_path = self._generate_smart_path(start_state, goal_state, obstacles)
        if reasonable_path and len(reasonable_path) > 3:  # 检查路径质量
            # 保存原始路径用于调试
            self.original_path = reasonable_path.copy()

            # 使用凸包优化路径点
            optimized_path = self._optimize_path_with_convex_hull(reasonable_path)
            if optimized_path:
                return optimized_path
            else:
                return reasonable_path

        # 策略2：使用Hybrid A*搜索算法
        path = self._hybrid_astar_search(start_state, goal_state, obstacles)
        if path and len(path) > 3:  # 检查搜索结果质量
            # 使用凸包优化路径点
            optimized_path = self._optimize_path_with_convex_hull(path)
            return optimized_path if optimized_path else path

        # 策略3：生成简单直线路径作为备选
        return self._generate_simple_path(start_state, goal_state)

    def _simple_astar(self, start_state, goal_state, obstacles):
        """真正的Hybrid A*搜索 - 考虑车辆运动学"""
        open_list = []
        closed_set = set()
        came_from = {}
        g_costs = {}

        start_node = self._state_to_node(start_state)
        g_costs[start_node] = 0
        h_cost = self._heuristic(start_state, goal_state)
        heapq.heappush(open_list, (h_cost, start_node, start_state))

        for _ in range(150):  # 适中的迭代次数
            if not open_list:
                break

            _, current_node, current_state = heapq.heappop(open_list)

            if current_node in closed_set:
                continue
            closed_set.add(current_node)

            # 目标检查
            if self._is_goal_reached(current_state, goal_state):
                path = self._reconstruct_path(came_from, current_node, start_node)
                path.append(current_state)  # 添加目标状态
                return path

            # 扩展邻居 - 使用车辆运动学
            for motion in self.motions:
                new_state = self._apply_motion_with_kinematics(current_state, motion)
                new_node = self._state_to_node(new_state)

                if new_node not in closed_set and self._is_collision_free(new_state, obstacles):
                    # 计算真实代价
                    motion_cost = self._motion_cost(motion)
                    tentative_g_cost = g_costs[current_node] + motion_cost

                    if new_node not in g_costs or tentative_g_cost < g_costs[new_node]:
                        g_costs[new_node] = tentative_g_cost
                        h_cost = self._heuristic(new_state, goal_state)
                        f_cost_new = tentative_g_cost + h_cost

                        came_from[new_node] = (current_node, new_state)
                        heapq.heappush(open_list, (f_cost_new, new_node, new_state))

        return None

    def _generate_simple_path(self, start_state, goal_state):
        """生成简单路径 - 确保有足够的引导点防止智能体撞墙"""
        x1, y1, theta1 = start_state
        x2, y2, theta2 = goal_state

        # 计算距离，决定中间点数量
        distance = np.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)

        # 根据距离动态确定中间点数量，确保至少有4个引导点
        # 这样可以给智能体足够的引导，避免直接撞墙
        num_intermediate_points = max(4, int(distance / 3.0))  # 每3米一个点，最少4个中间点

        path = []

        # 生成均匀分布的中间点
        for i in range(1, num_intermediate_points + 1):
            ratio = i / (num_intermediate_points + 1)

            # 位置线性插值
            mid_x = x1 + ratio * (x2 - x1)
            mid_y = y1 + ratio * (y2 - y1)

            # 朝向平滑插值 - 处理角度环绕问题
            angle_diff = theta2 - theta1
            # 选择最短的角度路径
            if angle_diff > np.pi:
                angle_diff -= 2 * np.pi
            elif angle_diff < -np.pi:
                angle_diff += 2 * np.pi

            mid_theta = theta1 + ratio * angle_diff
            mid_theta = np.arctan2(np.sin(mid_theta), np.cos(mid_theta))  # 规范化到[-π, π]

            path.append((mid_x, mid_y, mid_theta))

        # 添加终点
        path.append(goal_state)

        return path

    def _generate_smart_path(self, start_state: State, goal_state: State, obstacles: List[dict]) -> Optional[Path]:
        """智能路径生成：根据车头朝向和距离选择最适合的路径策略"""
        _ = obstacles  # 暂未使用障碍物信息
        try:
            # 解析起点和终点状态
            x1, y1, theta1 = start_state  # 起点坐标和朝向
            x2, y2, theta2 = goal_state   # 终点坐标和朝向

            # 计算起点到终点的直线距离
            distance = np.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)

            # 计算从起点到终点的方向角
            target_direction = np.arctan2(y2 - y1, x2 - x1)

            # 计算车头朝向与目标方向的角度差
            heading_diff = target_direction - theta1
            heading_diff = np.arctan2(np.sin(heading_diff), np.cos(heading_diff))  # 规范化到[-π,π]

            # 计算在车头方向上的位置偏差（关键改进）
            # 将目标点投影到垂直于车头方向的直线上，计算横向偏移
            lateral_offset = abs((x2 - x1) * np.sin(theta1) - (y2 - y1) * np.cos(theta1))

            # 计算在车头方向上的纵向距离
            longitudinal_distance = (x2 - x1) * np.cos(theta1) + (y2 - y1) * np.sin(theta1)

            # 计算最终朝向差异（停车场景很重要）
            final_heading_diff = abs(theta2 - theta1)
            final_heading_diff = min(final_heading_diff, 2*np.pi - final_heading_diff)

            # 根据角度差和位置对齐情况选择路径策略（收紧条件，更多使用弧线）
            if abs(heading_diff) < np.pi / 8:  # 角度差小于22.5度（收紧条件）
                # 收紧直线路径的条件，只有非常对齐的情况才用直线
                if (lateral_offset < 1.0 and longitudinal_distance > 2.0 and
                    final_heading_diff < np.pi/6 and distance < 8.0):
                    # 收紧条件：角度很小、横向偏移很小、距离较短
                    return self._generate_forward_path(start_state, goal_state)
                else:
                    # 使用弧线路径
                    return self._generate_arc_path(start_state, goal_state)

            elif abs(heading_diff) > np.pi / 3:  # 角度差大于60度（收紧条件）
                # 车头背离目标，需要复杂机动
                return self._generate_complex_path(start_state, goal_state)
            else:  # 角度差在22.5-60度之间，大部分情况用弧线
                if distance < 5.0 and lateral_offset > 6.0:
                    # 短距离但偏移很大：需要复杂机动
                    return self._generate_complex_path(start_state, goal_state)
                else:
                    # 其他情况：使用弧线路径（这应该是大部分情况）
                    return self._generate_arc_path(start_state, goal_state)

        except Exception:
            # 异常情况返回空路径
            return None

    def _generate_forward_path(self, start_state: State, goal_state: State) -> Path:
        """生成前进路径（小角度调整）- 不包含起点"""
        x1, y1, theta1 = start_state
        x2, y2, theta2 = goal_state

        path = []  # 不包含起点

        # 添加2-3个中间点，逐步调整朝向
        num_points = 3  # 减少点数
        for i in range(1, num_points):
            ratio = i / num_points

            # 位置插值
            x = x1 + ratio * (x2 - x1)
            y = y1 + ratio * (y2 - y1)

            # 朝向平滑插值
            theta = theta1 + ratio * (theta2 - theta1)
            theta = np.arctan2(np.sin(theta), np.cos(theta))

            path.append((x, y, theta))

        path.append(goal_state)
        return path

    def _generate_arc_path(self, start_state: State, goal_state: State) -> Path:
        """生成弧线路径（中等角度）"""
        x1, y1, theta1 = start_state
        x2, y2, theta2 = goal_state

        # 计算转弯中心
        distance = np.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)

        # 使用较大的转弯半径确保平滑
        _ = max(distance / 2, self.wheelbase * 3)  # turn_radius暂未使用

        path = []  # 不包含起点

        # 生成弧线上的点
        num_points = max(4, int(distance / 3))  # 减少点数

        for i in range(1, num_points):
            ratio = i / num_points

            # 使用三次贝塞尔曲线生成平滑路径
            # 控制点设计确保符合车辆运动学
            p0 = np.array([x1, y1])
            p3 = np.array([x2, y2])

            # 控制点基于起始和结束朝向
            control_distance = distance / 3
            p1 = p0 + control_distance * np.array([np.cos(theta1), np.sin(theta1)])
            p2 = p3 - control_distance * np.array([np.cos(theta2), np.sin(theta2)])

            # 贝塞尔曲线计算
            t = ratio
            pos = (1 - t) ** 3 * p0 + 3 * (1 - t) ** 2 * t * p1 + 3 * (1 - t) * t ** 2 * p2 + t ** 3 * p3

            # 计算该点的朝向（切线方向）
            if i < num_points - 1:
                # 计算切线方向
                derivative = 3 * (1 - t) ** 2 * (p1 - p0) + 6 * (1 - t) * t * (p2 - p1) + 3 * t ** 2 * (p3 - p2)
                theta = np.arctan2(derivative[1], derivative[0])
            else:
                theta = theta2

            path.append((pos[0], pos[1], theta))

        path.append(goal_state)
        return path

    def _generate_complex_path(self, start_state: State, goal_state: State) -> Path:
        """生成复杂机动路径（大角度或倒车）- 使用S型曲线"""
        x1, y1, theta1 = start_state
        x2, y2, theta2 = goal_state

        path = []  # 不包含起点
        distance = np.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)

        # 使用S型曲线处理大角度差异
        # 计算中间控制点，创建平滑的S型路径

        # 第一个控制点：沿起始方向延伸
        control_dist1 = distance * 0.4
        cp1_x = x1 + control_dist1 * np.cos(theta1)
        cp1_y = y1 + control_dist1 * np.sin(theta1)

        # 第二个控制点：沿目标方向反向延伸
        control_dist2 = distance * 0.4
        cp2_x = x2 - control_dist2 * np.cos(theta2)
        cp2_y = y2 - control_dist2 * np.sin(theta2)

        # 生成S型贝塞尔曲线路径
        num_points = max(5, int(distance / 3))

        for i in range(1, num_points):
            t = i / num_points

            # 四次贝塞尔曲线 (起点, 控制点1, 控制点2, 终点)
            p0 = np.array([x1, y1])
            p1 = np.array([cp1_x, cp1_y])
            p2 = np.array([cp2_x, cp2_y])
            p3 = np.array([x2, y2])

            # 贝塞尔曲线计算
            pos = ((1-t)**3 * p0 + 3*(1-t)**2*t * p1 +
                   3*(1-t)*t**2 * p2 + t**3 * p3)

            # 计算该点的朝向（切线方向）
            derivative = (3*(1-t)**2 * (p1-p0) + 6*(1-t)*t * (p2-p1) +
                         3*t**2 * (p3-p2))
            theta = np.arctan2(derivative[1], derivative[0])

            path.append((pos[0], pos[1], theta))

        path.append(goal_state)
        return path

    def _apply_motion_with_kinematics(self, state: Tuple[float, float, float],
                                      motion: Tuple[float, float]) -> Tuple[float, float, float]:
        """使用车辆运动学模型应用运动（Hybrid A*核心）"""
        x, y, theta = state
        distance, steering_angle = motion

        # 使用自行车模型（bicycle model）- Hybrid A*的核心
        if abs(steering_angle) < 0.01:  # 直行
            new_x = x + distance * np.cos(theta)
            new_y = y + distance * np.sin(theta)
            new_theta = theta
        else:
            # 计算转弯半径（考虑轴距）
            turning_radius = self.wheelbase / np.tan(abs(steering_angle))

            # 计算角度变化
            delta_theta = distance / turning_radius
            if steering_angle < 0:  # 右转
                delta_theta = -delta_theta

            # 计算新位置（使用圆弧运动）
            if distance > 0:  # 前进
                new_x = x + turning_radius * (np.sin(theta + delta_theta) - np.sin(theta))
                new_y = y - turning_radius * (np.cos(theta + delta_theta) - np.cos(theta))
            else:  # 后退
                new_x = x - turning_radius * (np.sin(theta + delta_theta) - np.sin(theta))
                new_y = y + turning_radius * (np.cos(theta + delta_theta) - np.cos(theta))

            new_theta = theta + delta_theta

        # 规范化角度到[-π, π]
        new_theta = np.arctan2(np.sin(new_theta), np.cos(new_theta))

        return (new_x, new_y, new_theta)

    def _apply_motion(self, state: Tuple[float, float, float],
                      motion: Tuple[float, float]) -> Tuple[float, float, float]:
        """应用运动原语 - 使用车辆运动学模型"""
        x, y, theta = state
        distance, steering_angle = motion

        # 限制转向角
        steering_angle = np.clip(steering_angle, -self.max_steering_angle, self.max_steering_angle)

        # 使用自行车模型（bicycle model）
        if abs(steering_angle) < 0.01:  # 直行
            new_x = x + distance * np.cos(theta)
            new_y = y + distance * np.sin(theta)
            new_theta = theta
        else:
            # 计算转弯半径
            turning_radius = self.wheelbase / np.tan(abs(steering_angle))

            # 计算角度变化
            delta_theta = distance / turning_radius
            if steering_angle < 0:  # 右转
                delta_theta = -delta_theta

            # 计算新位置
            if distance > 0:  # 前进
                new_x = x + turning_radius * (np.sin(theta + delta_theta) - np.sin(theta))
                new_y = y - turning_radius * (np.cos(theta + delta_theta) - np.cos(theta))
            else:  # 后退
                new_x = x - turning_radius * (np.sin(theta + delta_theta) - np.sin(theta))
                new_y = y + turning_radius * (np.cos(theta + delta_theta) - np.cos(theta))

            new_theta = theta + delta_theta

        # 规范化角度到[-π, π]
        new_theta = np.arctan2(np.sin(new_theta), np.cos(new_theta))

        return (new_x, new_y, new_theta)

    def _is_collision_free(self, state: Tuple[float, float, float],
                           obstacles: List[dict]) -> bool:
        """简单的碰撞检测"""
        x, y, _ = state

        for obs in obstacles:
            distance = np.linalg.norm(np.array([x, y]) - obs['position'])
            if distance < 3.5:  # 简单的安全距离
                return False

        return True

    def _heuristic(self, state: Tuple[float, float, float],
                   goal: Tuple[float, float, float]) -> float:
        """简化的启发式函数 - 主要考虑位置距离"""
        x1, y1, theta1 = state
        x2, y2, theta2 = goal

        # 位置距离（主要因素）
        position_distance = np.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)

        # 简化的朝向代价
        final_angle_diff = abs(theta2 - theta1)
        final_angle_diff = min(final_angle_diff, 2 * np.pi - final_angle_diff)
        angle_cost = 0.5 * final_angle_diff  # 降低朝向权重

        # 简化的组合代价
        return position_distance + angle_cost

    def _is_goal_reached(self, state: Tuple[float, float, float],
                         goal: Tuple[float, float, float]) -> bool:
        """检查是否到达目标 - 放宽条件提高搜索成功率"""
        x1, y1, theta1 = state
        x2, y2, theta2 = goal

        # 位置容差 - 进一步放宽
        position_distance = np.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
        position_tolerance = 4.0  # 4.0米位置容差

        # 朝向容差 - 进一步放宽
        angle_diff = abs(theta2 - theta1)
        angle_diff = min(angle_diff, 2 * np.pi - angle_diff)
        angle_tolerance = np.pi / 2  # 90度朝向容差

        # 同时满足位置和朝向要求
        return position_distance < position_tolerance and angle_diff < angle_tolerance

    def _state_to_node(self, state: Tuple[float, float, float]) -> Tuple[int, int, int]:
        """将状态转换为节点（用于哈希）- 降低精度提高搜索效率"""
        x, y, theta = state

        # 位置离散化：1米精度（降低精度）
        x_discrete = round(x)
        y_discrete = round(y)

        # 朝向离散化：30度精度（12个方向，降低精度）
        theta_normalized = np.arctan2(np.sin(theta), np.cos(theta))  # 规范化到[-π, π]
        theta_discrete = round(theta_normalized / (np.pi / 6)) * (np.pi / 6)

        return (int(x_discrete), int(y_discrete), int(theta_discrete / (np.pi / 6)))

    def _motion_cost(self, motion: Tuple[float, float]) -> float:
        """改进的运动代价 - 考虑距离、转向和方向"""
        distance, steering_angle = motion

        # 基础距离代价
        distance_cost = abs(distance)

        # 转向代价：转向角度越大代价越高
        steering_cost = 0.8 * abs(steering_angle)

        # 后退代价：后退比前进代价更高
        reverse_penalty = 0.3 if distance < 0 else 0

        # 急转弯代价：大角度转向额外惩罚
        sharp_turn_penalty = 0.5 if abs(steering_angle) > 0.4 else 0

        return distance_cost + steering_cost + reverse_penalty + sharp_turn_penalty

    def _reconstruct_path(self, came_from: dict, current: Tuple,
                          start: Tuple) -> List[Tuple[float, float, float]]:
        """重构路径"""
        path = []
        while current in came_from:
            parent_node, state = came_from[current]
            path.append(state)
            current = parent_node
            if current == start:
                break
        path.reverse()
        return path

    def _generate_intermediate_path(self, start_state: Tuple[float, float, float],
                                    goal_state: Tuple[float, float, float]) -> List[Tuple[float, float, float]]:
        """生成带中间点的路径"""
        x1, y1, theta1 = start_state
        x2, y2, theta2 = goal_state

        # 计算距离和方向
        distance = np.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)

        # 如果距离很近，直接连接
        if distance < 5.0:
            return [start_state, goal_state]

        # 生成中间点
        path = [start_state]

        # 添加1-2个中间点
        num_intermediate = min(2, int(distance / 8.0))

        for i in range(1, num_intermediate + 1):
            ratio = i / (num_intermediate + 1)

            # 线性插值位置
            x_mid = x1 + ratio * (x2 - x1)
            y_mid = y1 + ratio * (y2 - y1)

            # 插值朝向
            theta_mid = theta1 + ratio * (theta2 - theta1)

            # 规范化角度
            theta_mid = np.arctan2(np.sin(theta_mid), np.cos(theta_mid))

            path.append((x_mid, y_mid, theta_mid))

        path.append(goal_state)
        return path

    def _optimize_path_with_convex_hull(self, path: Path) -> Optional[Path]:
        """使用改进的算法优化路径，但确保保留关键拐点"""
        if not path or len(path) < 4:
            return path  # 路径太短，无需优化

        try:
            # 智能选择容忍度：根据路径复杂度和长度动态调整
            path_complexity = self._calculate_path_complexity(path)
            path_length = self._calculate_path_length(path)

            # 基础容忍度：根据路径复杂度确定
            if path_complexity > 0.8:  # 高复杂度路径（S型、U型等）
                base_tolerance = 0.8  # 小容忍度，保留重要弯曲
            elif path_complexity > 0.5:  # 中等复杂度路径
                base_tolerance = 1.2  # 中等容忍度
            elif path_complexity > 0.2:  # 轻微弯曲路径
                base_tolerance = 1.8  # 较大容忍度
            else:  # 简单路径（接近直线）
                base_tolerance = 2.5  # 大容忍度

            # 距离调整：近距离路径需要更激进的简化
            if path_length < 18:  # 短距离路径
                distance_multiplier = 1.5  # 增加容忍度，更激进简化
            elif path_length < 25:  # 中等距离路径
                distance_multiplier = 1.2
            else:  # 长距离路径
                distance_multiplier = 1.0

            tolerance = base_tolerance * distance_multiplier

            # 应用道格拉斯-普克算法
            simplified_path = self._douglas_peucker_simplify(path, tolerance=tolerance)

            # 智能处理简化结果
            if len(simplified_path) < 3:
                # 如果简化过度，尝试更小的容忍度
                tolerance *= 0.6
                simplified_path = self._douglas_peucker_simplify(path, tolerance=tolerance)

                # 如果还是太少，使用原路径但尝试手动优化
                if len(simplified_path) < 3:
                    # 根据距离和原始点数智能采样
                    if len(path) >= 4:  # 降低门槛，4个点就可以优化
                        if path_length < 18:  # 短距离：目标3个点
                            target_points = 3
                        elif path_length < 25:  # 中等距离：目标3-4个点
                            target_points = 3
                        else:  # 长距离：目标4-5个点
                            target_points = 4

                        # 均匀采样到目标点数
                        if len(path) > target_points:
                            step = (len(path) - 1) / (target_points - 1)
                            sampled_path = [path[0]]  # 起点
                            for i in range(1, target_points - 1):
                                idx = int(round(i * step))
                                sampled_path.append(path[idx])
                            sampled_path.append(path[-1])  # 终点
                            return sampled_path
                        elif len(path) == target_points:
                            return path
                    return path

            # 如果简化效果不明显，再尝试凸包优化
            if len(simplified_path) >= len(path) * 0.8:  # 如果只减少了20%以下
                hull_optimized = self._convex_hull_optimize(path)
                if hull_optimized and len(hull_optimized) >= 2 and len(hull_optimized) < len(simplified_path):
                    return hull_optimized

            return simplified_path if simplified_path else path

        except Exception:
            # 优化失败时返回原路径
            return path

    def _calculate_path_complexity(self, path: Path) -> float:
        """计算路径复杂度（0-1之间，1表示最复杂）"""
        if len(path) < 3:
            return 0.0

        # 计算路径的总弯曲度
        total_curvature = 0.0
        total_distance = 0.0

        for i in range(1, len(path) - 1):
            # 计算三个连续点的角度变化
            p1 = np.array([path[i-1][0], path[i-1][1]])
            p2 = np.array([path[i][0], path[i][1]])
            p3 = np.array([path[i+1][0], path[i+1][1]])

            # 计算向量
            v1 = p2 - p1
            v2 = p3 - p2

            # 避免零向量
            if np.linalg.norm(v1) < 1e-6 or np.linalg.norm(v2) < 1e-6:
                continue

            # 计算角度变化（弯曲度）
            cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
            cos_angle = np.clip(cos_angle, -1.0, 1.0)
            angle_change = np.arccos(cos_angle)

            # 累积弯曲度，按距离加权
            segment_distance = np.linalg.norm(v2)
            total_curvature += angle_change * segment_distance
            total_distance += segment_distance

        if total_distance < 1e-6:
            return 0.0

        # 归一化复杂度（除以理论最大值）
        normalized_complexity = total_curvature / (total_distance * np.pi)
        return min(1.0, normalized_complexity)

    def _calculate_path_length(self, path: Path) -> float:
        """计算路径总长度"""
        if len(path) < 2:
            return 0.0

        total_length = 0.0
        for i in range(1, len(path)):
            p1 = np.array([path[i-1][0], path[i-1][1]])
            p2 = np.array([path[i][0], path[i][1]])
            segment_length = np.linalg.norm(p2 - p1)
            total_length += segment_length

        return total_length

    def _douglas_peucker_simplify(self, path: Path, tolerance: float = 1.5) -> Path:
        """使用道格拉斯-普克算法简化路径"""
        if len(path) <= 2:
            return path

        # 提取x,y坐标
        points = np.array([[state[0], state[1]] for state in path])

        # 应用道格拉斯-普克算法
        simplified_indices = self._douglas_peucker_recursive(points, 0, len(points)-1, tolerance)
        simplified_indices = sorted(list(set(simplified_indices)))

        # 构建简化后的路径，保留朝向信息
        simplified_path = [path[i] for i in simplified_indices]

        return simplified_path

    def _douglas_peucker_recursive(self, points: np.ndarray, start: int, end: int, tolerance: float) -> set:
        """道格拉斯-普克算法递归实现"""
        if end - start <= 1:
            return {start, end}

        # 找到距离起点-终点连线最远的点
        max_distance = 0
        max_index = start

        for i in range(start + 1, end):
            distance = self._point_to_line_distance(points[i], points[start], points[end])
            if distance > max_distance:
                max_distance = distance
                max_index = i

        # 如果最大距离超过容忍度，递归处理
        if max_distance > tolerance:
            left_indices = self._douglas_peucker_recursive(points, start, max_index, tolerance)
            right_indices = self._douglas_peucker_recursive(points, max_index, end, tolerance)
            return left_indices.union(right_indices)
        else:
            return {start, end}

    def _point_to_line_distance(self, point: np.ndarray, line_start: np.ndarray, line_end: np.ndarray) -> float:
        """计算点到直线的距离"""
        if np.allclose(line_start, line_end):
            return np.linalg.norm(point - line_start)

        # 使用向量叉积计算距离
        line_vec = line_end - line_start
        point_vec = point - line_start

        # 计算叉积的模长除以线段长度
        cross_product = np.abs(np.cross(line_vec, point_vec))
        line_length = np.linalg.norm(line_vec)

        return cross_product / line_length

    def _convex_hull_optimize(self, path: Path) -> Optional[Path]:
        """使用凸包算法优化路径（原有方法）"""
        try:
            # 提取路径的x,y坐标
            points = np.array([[state[0], state[1]] for state in path])

            # 检查是否有足够的不重复点来构建凸包
            unique_points = np.unique(points, axis=0)
            if len(unique_points) < 3:
                return path  # 点太少，无法构建凸包

            # 计算凸包
            hull = ConvexHull(unique_points)
            hull_indices = hull.vertices

            # 获取凸包上的点
            hull_points = unique_points[hull_indices]

            # 按照原路径的顺序重新排列凸包点
            optimized_path = self._reorder_hull_points_by_path(path, hull_points)

            # 确保起点和终点被保留
            if optimized_path:
                optimized_path = self._ensure_start_end_points(path, optimized_path)

            return optimized_path if optimized_path and len(optimized_path) >= 2 else path

        except Exception:
            return None

    def _reorder_hull_points_by_path(self, original_path: Path, hull_points: np.ndarray) -> Optional[Path]:
        """按照原路径顺序重新排列凸包点"""
        try:
            # 为每个凸包点找到在原路径中最接近的点的索引
            hull_with_indices = []

            for hull_point in hull_points:
                min_dist = float('inf')
                best_index = 0

                for i, path_state in enumerate(original_path):
                    dist = np.linalg.norm([path_state[0] - hull_point[0],
                                         path_state[1] - hull_point[1]])
                    if dist < min_dist:
                        min_dist = dist
                        best_index = i

                hull_with_indices.append((best_index, hull_point))

            # 按照在原路径中的索引排序
            hull_with_indices.sort(key=lambda x: x[0])

            # 构建优化后的路径，保留朝向信息
            optimized_path = []
            for index, hull_point in hull_with_indices:
                # 使用原路径中对应点的朝向
                original_theta = original_path[index][2]
                optimized_path.append((hull_point[0], hull_point[1], original_theta))

            return optimized_path

        except Exception:
            return None

    def _ensure_start_end_points(self, original_path: Path, optimized_path: Path) -> Path:
        """确保起点和终点被保留在优化后的路径中"""
        if not optimized_path:
            return original_path

        start_point = original_path[0]
        end_point = original_path[-1]

        # 检查起点是否在优化路径中
        start_in_path = any(np.linalg.norm([p[0] - start_point[0], p[1] - start_point[1]]) < 0.5
                           for p in optimized_path)

        # 检查终点是否在优化路径中
        end_in_path = any(np.linalg.norm([p[0] - end_point[0], p[1] - end_point[1]]) < 0.5
                         for p in optimized_path)

        result_path = list(optimized_path)

        # 如果起点不在路径中，添加到开头
        if not start_in_path:
            result_path.insert(0, start_point)

        # 如果终点不在路径中，添加到末尾
        if not end_in_path:
            result_path.append(end_point)

        return result_path


class CustomParkingEnv(ParkingEnv):
    """
    自定义停车环境，继承自 HighwayEnv 的 ParkingEnv
    集成基础版Hybrid A*路径规划和平滑度控制
    """

    def __init__(self, config):
        # 保存原始配置
        self.custom_config = config
        self.num_envs = 1

        # 初始化扁平化标志
        self._flatten_obs = False

        # 初始化路径规划器和相关变量
        self.path_planner = SimpleHybridAStar()
        self.reference_path = None
        self.path_index = 0
        self.last_action = None
        self.last_raw_action = None  # 用于动作平滑





        # 准备传递给父类的配置
        env_config = self._prepare_config(config)
        render_mode = getattr(config, 'render_mode', 'human')

        # 调用父类初始化
        super(CustomParkingEnv, self).__init__(config=env_config, render_mode=render_mode)

        # 处理观察空间 - 如果是Dict类型，转换为扁平化的Box
        self._setup_observation_space()

        # 限制动作空间，避免极端动作
        self.action_space = gym.spaces.Box(
            low=np.array([-1.5, -0.3]),  # [加速度, 转向]
            high=np.array([1.5, 0.3]),
            dtype=np.float32
        )

        # 设置最大步数
        self.max_episode_steps = getattr(config, 'max_episode_steps', 100)

    def _prepare_config(self, config):
        """准备传递给父类的配置"""
        # 获取默认配置
        env_config = self.default_config()

        # 更新自定义配置
        if hasattr(config, 'env_seed'):
            env_config['seed'] = config.env_seed

        # 支持自定义episode最大步数
        if hasattr(config, 'max_episode_steps') and config.max_episode_steps is not None:
            env_config['duration'] = config.max_episode_steps

        # 支持自定义碰撞惩罚
        if hasattr(config, 'collision_reward') and config.collision_reward is not None:
            env_config['collision_reward'] = config.collision_reward

        return env_config

    def _setup_observation_space(self):
        """设置观察空间，支持扁平化"""
        if isinstance(self.observation_space, gym.spaces.Dict):
            # 计算总的观察维度
            total_dim = 0
            for space in self.observation_space.spaces.values():
                if isinstance(space, gym.spaces.Box):
                    total_dim += np.prod(space.shape)

            # 创建扁平化的观察空间
            self.observation_space = gym.spaces.Box(
                low=-np.inf,
                high=np.inf,
                shape=(total_dim,),
                dtype=np.float32
            )
            self._flatten_obs = True
        else:
            self._flatten_obs = False

    def _flatten_observation(self, obs):
        """扁平化观察"""
        if self._flatten_obs and isinstance(obs, dict):
            # 将Dict观察扁平化为一维数组
            flat_obs = []
            for key in sorted(obs.keys()):  # 保证顺序一致
                if isinstance(obs[key], np.ndarray):
                    flat_obs.append(obs[key].flatten())
                else:
                    flat_obs.append(np.array([obs[key]]).flatten())
            return np.concatenate(flat_obs).astype(np.float32)
        return obs

    def reset(self, *, seed: Optional[int] = None, options: Optional[dict] = None):
        """重置环境并规划路径"""
        # 如果提供了种子，使用自定义配置中的种子
        if seed is None and hasattr(self.custom_config, 'env_seed'):
            seed = self.custom_config.env_seed

        # 为了确保每次重置时有不同的随机性，在基础种子上添加时间戳
        if seed is not None:
            import time
            seed = seed + int(time.time() * 1000) % 10000

        obs, info = super().reset(seed=seed, options=options)

        # 规划路径
        self._plan_path()



        # 重置平滑度控制变量
        self.last_action = None
        self.last_raw_action = None
        self.path_index = 0

        obs = self._flatten_observation(obs)

        # 添加路径信息到info中
        if self.reference_path:
            info.update({
                'path_length': len(self.reference_path),
                'path_planned': True
            })
        else:
            info.update({
                'path_length': 0,
                'path_planned': False
            })

        return obs, info



    def _plan_path(self):
        """使用Hybrid A*规划路径"""
        try:
            vehicle = self.controlled_vehicles[0]
            start_state = (vehicle.position[0], vehicle.position[1], vehicle.heading)
            goal_state = (vehicle.goal.position[0], vehicle.goal.position[1], vehicle.goal.heading)

            # 获取障碍物
            obstacles = self._get_obstacles()

            # 使用Hybrid A*规划
            self.reference_path = self.path_planner.plan(start_state, goal_state, obstacles)

            if self.reference_path is None or len(self.reference_path) <2:
                # 如果规划失败，使用简单的直线路径
                self.reference_path = [start_state, goal_state]

        except Exception:
            # 出错时使用简单路径
            vehicle = self.controlled_vehicles[0]
            start_state = (vehicle.position[0], vehicle.position[1], vehicle.heading)
            goal_state = (vehicle.goal.position[0], vehicle.goal.position[1], vehicle.goal.heading)
            self.reference_path = [start_state, goal_state]

    def _get_obstacles(self):
        """获取障碍物信息"""
        obstacles = []

        try:
            # 其他车辆作为障碍物
            for vehicle in self.road.vehicles:
                if vehicle != self.controlled_vehicles[0]:
                    obstacles.append({
                        'position': vehicle.position,
                        'type': 'vehicle'
                    })
        except:
            pass  # 如果获取障碍物失败，返回空列表

        return obstacles

    def _get_distance_to_path(self):
        """获取车辆到规划路径的最小距离和最近路径点信息"""
        # 检查是否存在有效的规划路径
        if not self.reference_path:
            return float('inf'), -1, None  # 无路径时返回无穷大距离

        try:
            # 获取当前被控制的车辆对象
            vehicle = self.controlled_vehicles[0]

            # 初始化搜索变量
            min_distance = float('inf')    # 最小距离，初始为无穷大
            closest_index = 0              # 最近路径点的索引
            closest_waypoint = None        # 最近的路径点坐标

            # 遍历规划路径中的每个路径点
            for i, waypoint in enumerate(self.reference_path):
                # 计算车辆当前位置到路径点的欧几里得距离
                # waypoint[:2] 提取路径点的x,y坐标（忽略朝向角度）
                # vehicle.position 是车辆当前的[x, y]坐标
                distance = np.linalg.norm(vehicle.position - np.array(waypoint[:2]))

                # 如果当前距离小于已记录的最小距离，更新最近点信息
                if distance < min_distance:
                    min_distance = distance      # 更新最小距离
                    closest_index = i           # 更新最近点索引
                    closest_waypoint = waypoint # 更新最近点坐标

            # 返回三个值：最小距离、最近点索引、最近点坐标
            return min_distance, closest_index, closest_waypoint

        except:
            # 异常情况下返回安全默认值
            return float('inf'), -1, None

    def step(self, action):
        """执行动作并获取下一个观察、奖励和其他信息"""
        # 简单的动作平滑
        original_action = np.array(action)
        if hasattr(self, 'last_raw_action') and self.last_raw_action is not None:
            action = 0.7 * np.array(action) + 0.3 * np.array(self.last_raw_action)
        self.last_raw_action = original_action.copy()

        obs, reward, terminated, truncated, info = super().step(action)

        # 增强的速度控制机制：添加自然阻力 + 智能减速
        try:
            vehicle = self.controlled_vehicles[0]
            if not vehicle.crashed:
                dt = 1.0 / self.config.get("simulation_frequency", 15)
                current_speed = vehicle.speed
                goal_distance = np.linalg.norm(vehicle.goal.position - vehicle.position)

                # 1. 添加全局自然阻力（模拟摩擦力和空气阻力）
                natural_drag_coefficient = 0.15  # 自然阻力系数
                if abs(current_speed) > 0.01:  # 避免在零速度时产生数值误差
                    # 阻力与速度成正比，方向相反
                    drag_force = -natural_drag_coefficient * current_speed
                    vehicle.speed += drag_force * dt

                # 2. 智能减速区域（扩大减速区域，增强减速力度）
                deceleration_zone = 12.0  # 扩大到12米开始减速

                if goal_distance <= deceleration_zone:
                    # 计算减速强度（距离越近减速越强）
                    distance_ratio = goal_distance / deceleration_zone
                    # 使用非线性减速曲线，接近目标时减速更强
                    deceleration_strength = 1.5 * (1.0 - distance_ratio ** 0.5)

                    # 速度越高，减速越强
                    speed_factor = min(2.0, abs(current_speed) / 2.0 + 0.5)
                    total_deceleration = deceleration_strength * speed_factor

                    if abs(current_speed) > 0.05:  # 提高速度阈值
                        # 根据速度方向给相反的减速度
                        if current_speed > 0:
                            deceleration = -total_deceleration
                        else:
                            deceleration = total_deceleration

                        # 应用减速
                        vehicle.speed += deceleration * dt

                        # 防止速度反向并在极低速度时直接停止
                        if abs(vehicle.speed) < 0.1:  # 低于0.1 m/s时直接停止
                            vehicle.speed = 0
                        elif current_speed > 0 and vehicle.speed < 0:
                            vehicle.speed = 0
                        elif current_speed < 0 and vehicle.speed > 0:
                            vehicle.speed = 0

                # 3. 极近距离强制停车（最后的安全措施）
                if goal_distance < 2.0 and abs(current_speed) > 0.2:
                    # 在2米内且速度还很高时，强制大幅减速
                    emergency_brake = -2.0 * np.sign(current_speed)
                    vehicle.speed += emergency_brake * dt
                    if abs(vehicle.speed) < 0.05:
                        vehicle.speed = 0

        except:
            pass

        obs = self._flatten_observation(obs)

        # 添加详细的调试信息到info中
        try:
            # 计算各项奖励分量
            base_reward_value = super()._reward(action)
            path_reward_value = self._compute_path_following_reward()
            smoothness_reward_value = self._compute_smoothness_reward(action)

            if self.reference_path:
                vehicle = self.controlled_vehicles[0]
                min_dist = min(np.linalg.norm(vehicle.position - np.array(wp[:2]))
                               for wp in self.reference_path)

                info.update({
                    'path_distance': min_dist,
                    'path_index': self.path_index,
                    'path_following_reward': path_reward_value
                })

            # 添加平滑度和动作信息
            if self.last_action is not None:
                action_change = np.linalg.norm(np.array(action) - np.array(self.last_action))
                info.update({
                    'action_change': action_change,
                    'smoothness_reward': smoothness_reward_value,
                    'is_smooth_action': action_change < 0.3
                })

            # 添加奖励分解信息
            info.update({
                'reward_breakdown': {
                    'base_reward': base_reward_value,
                    'path_reward': path_reward_value,
                    'smoothness_reward': smoothness_reward_value,
                    'total_reward': reward
                },
                'original_action': original_action,
                'smoothed_action': action,
                'action_smoothing_applied': np.linalg.norm(original_action - action) > 0.001
            })
        except:
            pass  # 如果添加信息失败，不影响主要功能

        # 标记需要重新绘制路径
        self._need_redraw_path = True

        return obs, reward, terminated, truncated, info

    # ============================================================================
    # 奖励和碰撞相关方法 - 继承自父类ParkingEnv
    # ============================================================================

    def _reward(self, action: np.ndarray) -> float:
        """计算综合奖励函数"""
        # 获取环境基础奖励（距离、成功、碰撞等）
        base_reward = super()._reward(action)

        # 计算路径跟踪奖励（鼓励沿规划路径行驶）
        path_reward = self._compute_path_following_reward()

        # 计算动作平滑度奖励（鼓励平滑驾驶）
        smoothness_reward = self._compute_smoothness_reward(action)

        # 计算停车专用奖励（鼓励在目标附近减速停车）
        parking_reward = self._compute_parking_reward()

        # 加权组合四种奖励
        total_reward = (
                0.25 * base_reward +      # 基础任务完成奖励
                0.45 * path_reward +      # 路径跟踪奖励
                0.1 * smoothness_reward + # 平滑驾驶奖励
                0.2 * parking_reward      # 停车专用奖励（新增）
        )

        return total_reward

    def _compute_parking_reward(self):
        """停车专用奖励：鼓励在目标附近减速和停车"""
        try:
            vehicle = self.controlled_vehicles[0]
            current_speed = abs(vehicle.speed)
            goal_distance = np.linalg.norm(vehicle.goal.position - vehicle.position)

            parking_reward = 0.0

            # 1. 距离-速度匹配奖励：距离越近，速度应该越慢
            if goal_distance < 15.0:
                # 计算期望速度（距离越近期望速度越低）
                expected_speed = max(0.1, goal_distance / 15.0 * 2.0)  # 15米处期望2m/s，0米处期望0.1m/s

                speed_diff = abs(current_speed - expected_speed)
                if speed_diff < 0.5:  # 速度接近期望
                    parking_reward += 0.1 - speed_diff * 0.1
                else:  # 速度偏离期望
                    parking_reward -= speed_diff * 0.05

            # 2. 近距离停车奖励
            if goal_distance < 5.0:
                if current_speed < 0.3:  # 低速接近
                    parking_reward += 0.15 * (1.0 - current_speed / 0.3)
                else:  # 高速接近，惩罚
                    parking_reward -= 0.1

            # 3. 精确停车奖励
            if goal_distance < 2.0:
                if current_speed < 0.15:  # 几乎停止
                    parking_reward += 0.2 * (1.0 - current_speed / 0.15)
                    # 距离越近奖励越高
                    parking_reward += 0.1 * (1.0 - goal_distance / 2.0)

            # 4. 完美停车奖励
            if goal_distance < 1.0 and current_speed < 0.1:
                parking_reward += 0.3  # 大奖励

            return parking_reward

        except:
            return 0.0

    def _is_success(self, achieved_goal: np.ndarray, desired_goal: np.ndarray) -> bool:
        """
        成功判定：到达车位且停稳

        参数:
            achieved_goal: 实际达到的目标状态
            desired_goal: 期望的目标状态
        返回:
            bool: 是否成功（到达车位 + 停稳）
        """
        try:
            # 1. 检查是否到达车位（位置准确）
            position_success = (
                self.compute_reward(achieved_goal, desired_goal, {})
                > -self.config["success_goal_reward"]
            )

            if not position_success:
                return False  # 位置不满足，直接失败

            # 2. 检查是否停稳（速度很低）- 放宽条件
            vehicle = self.controlled_vehicles[0]
            current_speed = np.linalg.norm(vehicle.velocity)
            goal_distance = np.linalg.norm(vehicle.goal.position - vehicle.position)

            # 动态调整停稳标准：距离越近，速度要求越严格
            if goal_distance < 1.0:
                # 极近距离：严格要求
                speed_threshold = 0.15  # 0.15 m/s
            elif goal_distance < 2.0:
                # 近距离：中等要求
                speed_threshold = 0.25  # 0.25 m/s
            else:
                # 一般距离：宽松要求
                speed_threshold = 0.4   # 0.4 m/s

            stopped = current_speed < speed_threshold

            # 到达车位 + 停稳 = 成功
            return position_success and stopped

        except:
            # 异常情况返回父类的判定结果
            return super()._is_success(achieved_goal, desired_goal)

    def _compute_path_following_reward(self):
        """计算路径跟踪奖励：基于车辆到规划路径的距离"""
        # 检查是否有有效的规划路径
        if not self.reference_path or len(self.reference_path) < 2:
            return 0.0

        try:
            # 获取当前控制车辆
            _ = self.controlled_vehicles[0]  # vehicle暂未使用

            # 计算车辆到路径的最短距离和最近路径点
            min_distance, closest_index, _ = self._get_distance_to_path()

            # 检查是否找到有效的最近点
            if closest_index == -1:
                return 0.0

            # 更新当前路径索引（用于跟踪进度）
            self.path_index = closest_index

            # 基于距离的奖励
            # 基于距离计算奖励（放宽约束，鼓励前进）
            distance_reward = 0
            if min_distance < 4.0:
                # 在路径附近：给予奖励，鼓励保持
                distance_reward = 0.2 - min_distance * 0.03  # 0米=0.2分，4米=0.08分
            elif min_distance < 8.0:
                # 中等偏离：轻微惩罚，但不阻止前进
                distance_reward = -0.02 - (min_distance - 4.0) * 0.01  # 4米=-0.02分，8米=-0.06分
            else:
                # 严重偏离：适度惩罚
                distance_reward = -0.1  # 超过8米惩罚

            # 添加前进激励奖励
            progress_reward = self._compute_progress_reward()

            return distance_reward + progress_reward

        except:
            # 异常情况返回零奖励
            return 0.0

    def _compute_progress_reward(self):
        """计算前进进度奖励：鼓励车辆沿路径前进"""
        if not hasattr(self, 'last_path_index'):
            self.last_path_index = 0
            return 0.0

        try:
            # 获取当前路径索引
            _, current_index, _ = self._get_distance_to_path()

            if current_index == -1:
                return 0.0

            # 计算路径进度变化
            progress_change = current_index - self.last_path_index

            # 更新记录
            self.last_path_index = current_index

            # 前进奖励
            if progress_change > 0:
                return 0.05 * progress_change  # 每前进一个路径点奖励0.05
            elif progress_change < 0:
                return -0.02 * abs(progress_change)  # 后退轻微惩罚
            else:
                return 0.0  # 停留在同一点无奖励无惩罚

        except:
            return 0.0

    def _compute_smoothness_reward(self, action):
        """细化的平滑度奖励：分别优化速度控制和转向控制"""
        # 首次调用时初始化上一次动作
        if self.last_action is None:
            self.last_action = action.copy() if hasattr(action, 'copy') else np.array(action)
            return 0.0

        try:
            # 分别计算速度和转向的平滑度奖励
            speed_reward = self._compute_speed_control_reward(action)
            steering_reward = self._compute_steering_control_reward(action)

            # 保存当前动作供下次比较
            self.last_action = action.copy() if hasattr(action, 'copy') else np.array(action)

            # 组合平滑度奖励（速度控制更重要）
            return 0.6 * speed_reward + 0.4 * steering_reward

        except:
            # 异常情况返回零奖励
            return 0.0

    def _compute_speed_control_reward(self, action):
        """改进的速度控制奖励：根据距离目标的远近动态调整速度策略"""
        acceleration = action[0]
        last_acceleration = self.last_action[0]

        speed_penalty = 0.0

        try:
            # 获取当前车辆状态
            vehicle = self.controlled_vehicles[0]
            current_speed = abs(vehicle.speed)
            goal_distance = np.linalg.norm(vehicle.goal.position - vehicle.position)

            # 根据距离目标的远近动态调整速度策略
            if goal_distance > 15.0:
                # 远距离：鼓励适中前进速度
                if abs(acceleration) > 1.0:  # 过快
                    speed_penalty += (abs(acceleration) - 1.0) * 0.3
                elif abs(acceleration) < 0.02:  # 太慢
                    speed_penalty += 0.06
                elif 0.2 < abs(acceleration) < 0.8:  # 适中
                    speed_penalty -= 0.03

            elif goal_distance > 8.0:
                # 中距离：开始鼓励减速
                if abs(acceleration) > 0.6:  # 速度过快
                    speed_penalty += (abs(acceleration) - 0.6) * 0.5
                elif abs(acceleration) < 0.02:  # 太慢
                    speed_penalty += 0.04
                elif 0.1 < abs(acceleration) < 0.4:  # 适中
                    speed_penalty -= 0.02

            elif goal_distance > 3.0:
                # 近距离：强烈鼓励减速
                if abs(acceleration) > 0.3:  # 还在加速
                    speed_penalty += (abs(acceleration) - 0.3) * 0.8
                elif acceleration < -0.1:  # 在减速，给奖励
                    speed_penalty -= 0.05
                elif abs(acceleration) < 0.05:  # 几乎不动，也给奖励
                    speed_penalty -= 0.03

            else:
                # 极近距离：强烈鼓励停车
                if abs(acceleration) > 0.1:  # 还在动
                    speed_penalty += abs(acceleration) * 1.0
                elif abs(acceleration) < 0.02:  # 几乎停止
                    speed_penalty -= 0.08
                # 额外的速度惩罚
                if current_speed > 0.5:  # 速度还很高
                    speed_penalty += current_speed * 0.5

            # 2. 平滑加速奖励（防止急加速/急减速）
            accel_change = abs(acceleration - last_acceleration)
            if accel_change > 0.4:  # 加速度变化过大
                speed_penalty += accel_change * 0.4
            elif accel_change < 0.1:  # 加速度变化平滑
                speed_penalty -= 0.01

        except:
            # 异常情况使用原始逻辑
            if abs(acceleration) > 0.8:
                speed_penalty += (abs(acceleration) - 0.8) * 0.4
            elif abs(acceleration) < 0.02:
                speed_penalty += 0.08
            elif 0.1 < abs(acceleration) < 0.5:
                speed_penalty -= 0.02

        return -speed_penalty

    def _compute_steering_control_reward(self, action):
        """转向控制奖励：鼓励渐进式转向和有效转向"""
        if len(action) < 2:
            return 0.0

        steering = action[1]
        last_steering = self.last_action[1] if len(self.last_action) > 1 else 0

        steering_penalty = 0.0
        steering_bonus = 0.0

        # 1. 转向变化平滑度（防止急转向）
        steering_change = abs(steering - last_steering)
        if steering_change > 0.3:  # 转向变化过大
            steering_penalty += steering_change * 0.4
        elif steering_change < 0.05:  # 转向变化平滑
            steering_bonus += 0.01

        # 2. 渐进转向奖励（鼓励逐步调整）
        steer_magnitude = abs(steering)
        if 0.05 < steer_magnitude < 0.4:  # 适中的转向
            steering_bonus += 0.02
        elif steer_magnitude > 0.7:  # 过度转向
            steering_penalty += (steer_magnitude - 0.7) * 0.5

        # 3. 转向有效性奖励
        if self.reference_path:
            effectiveness = self._evaluate_steering_effectiveness(action)
            if effectiveness > 0.7:  # 高效转向
                steering_bonus += 0.03
            elif effectiveness < 0.3:  # 低效转向
                steering_penalty += 0.02

        # 4. 直线行驶时的转向惩罚
        if self._is_straight_path_ahead() and steer_magnitude > 0.2:
            steering_penalty += steer_magnitude * 0.3  # 直线时不应大幅转向

        return -steering_penalty + steering_bonus

    def _is_straight_path_ahead(self):
        """判断前方路径是否为直线（用于转向控制）"""
        if not self.reference_path or len(self.reference_path) < 3:
            return True  # 路径太短，默认为直线

        try:
            # 获取当前位置在路径中的索引
            _, current_index, _ = self._get_distance_to_path()

            # 检查前方3个路径点的角度变化
            if current_index >= 0 and current_index + 2 < len(self.reference_path):
                # 计算连续路径段的角度变化
                p1 = self.reference_path[current_index]
                p2 = self.reference_path[current_index + 1]
                p3 = self.reference_path[current_index + 2]

                # 计算两个路径段的方向角
                angle1 = np.arctan2(p2[1] - p1[1], p2[0] - p1[0])
                angle2 = np.arctan2(p3[1] - p2[1], p3[0] - p2[0])

                # 计算角度变化
                angle_change = abs(angle2 - angle1)
                angle_change = min(angle_change, 2*np.pi - angle_change)

                # 角度变化小于15度认为是直线
                return angle_change < np.pi/12

            return True

        except:
            return True

    def _evaluate_steering_effectiveness(self, action):
        """评估转向动作的有效性：判断转向是否朝向正确方向"""
        # 检查输入有效性
        if not self.reference_path or len(action) < 2:
            return 0.0

        try:
            # 获取当前车辆状态
            vehicle = self.controlled_vehicles[0]
            steering_angle = action[1]  # 转向角度（正值=左转，负值=右转）

            # 获取最近的路径点
            _, _, closest_waypoint = self._get_distance_to_path()

            # 检查是否找到有效路径点
            if closest_waypoint is None:
                return 0.0

            # 计算从当前位置到目标路径点的方向
            target_direction = np.arctan2(
                closest_waypoint[1] - vehicle.position[1],  # y方向差
                closest_waypoint[0] - vehicle.position[0]   # x方向差
            )

            # 计算车头朝向与目标方向的角度差
            heading_diff = target_direction - vehicle.heading
            heading_diff = np.arctan2(np.sin(heading_diff), np.cos(heading_diff))  # 规范化到[-π,π]

            # 评估转向有效性
            if abs(heading_diff) < 0.1:  # 车头已经基本朝向目标（约6度内）
                # 不需要转向时，小转向=好，大转向=差
                return 1.0 if abs(steering_angle) < 0.1 else 0.2
            elif heading_diff > 0:  # 目标在左侧，需要左转
                # 左转有效性：正转向角度越大越好
                return max(0, steering_angle) / 0.6
            else:  # 目标在右侧，需要右转
                # 右转有效性：负转向角度绝对值越大越好
                return max(0, -steering_angle) / 0.6

        except:
            # 异常情况返回无效
            return 0.0

    def _draw_planned_path_on_surface(self, surface):
        """在指定surface上绘制规划路径"""
        try:
            # 检查基本条件
            if not self.reference_path or len(self.reference_path) < 2:
                return

            # 导入pygame
            import pygame

            # 转换路径点为屏幕坐标
            screen_points = []
            for waypoint in self.reference_path:
                x, y = waypoint[:2]
                screen_x, screen_y = surface.pos2pix(x, y)
                screen_points.append((int(screen_x), int(screen_y)))

            if len(screen_points) < 2:
                return

            # 绘制路径线（红色粗线 - 表示Hybrid A*规划路径）
            pygame.draw.lines(surface, (255, 50, 50), False, screen_points, 6)

            # 绘制起点（绿色圆圈）
            pygame.draw.circle(surface, (50, 255, 50), screen_points[0], 12)
            pygame.draw.circle(surface, (255, 255, 255), screen_points[0], 12, 2)

            # 绘制终点（蓝色方块 - 目标停车位）
            target_size = 10
            target_rect = pygame.Rect(
                screen_points[-1][0] - target_size,
                screen_points[-1][1] - target_size,
                target_size * 2,
                target_size * 2
            )
            pygame.draw.rect(surface, (50, 150, 255), target_rect)
            pygame.draw.rect(surface, (255, 255, 255), target_rect, 2)

            # 绘制路径点（小红色圆点）
            for i in range(1, len(screen_points) - 1):
                pygame.draw.circle(surface, (200, 100, 100), screen_points[i], 4)

        except Exception:
            pass  # 静默处理绘制错误

    # ============================================================================
    # 配置和默认设置相关方法 - 摄像头固定中央功能
    # ============================================================================

    @classmethod
    def default_config(cls) -> dict:
        """
        默认环境配置
        重写自 ParkingEnv，设置固定摄像头视角

        Returns:
            dict: 默认配置字典
        """
        config = super(CustomParkingEnv, cls).default_config()
        # 摄像头配置 - 固定在中央而不是跟随车辆
        config.update({
            "centering_position": [0.5, 0.5],  # 摄像头居中位置
            "scaling": 8,  # 缩放比例（增加以显示更多细节）
            "screen_width": 1200,  # 屏幕宽度（增加）
            "screen_height": 800,  # 屏幕高度（增加）
            # 禁用车辆跟随模式
            "offscreen_rendering": False,
        })
        return config

    def render(self, mode='human'):
        """
        重写渲染方法，设置固定摄像头视角并绘制路径
        """
        _ = mode  # 标记为故意未使用
        # 强制重新创建viewer以应用新的配置
        if self.viewer is None:
            from HighwayEnv.highway_env.envs.common.graphics import EnvViewer
            self.viewer = EnvViewer(self, config=self.config)

            # 使用agent_display机制来绘制路径
            def path_display(agent_surface, sim_surface):
                """使用agent_display机制绘制路径"""
                # agent_surface暂时不用，只在sim_surface上绘制
                _ = agent_surface  # 标记为故意未使用
                self._draw_planned_path_on_surface(sim_surface)

            # 设置agent_display
            self.viewer.set_agent_display(path_display)

        # 设置固定的观察者位置（摄像头固定在中央）
        self.viewer.observer_vehicle = None  # 清除跟随车辆

        def fixed_window_position():
            # 返回固定的中央位置 [0, 0] 表示停车场中心
            return np.array([0.0, 0.0])

        # 临时替换 window_position 方法
        self.viewer.window_position = fixed_window_position

        # 调用父类的渲染方法
        result = super().render()

        return result


def CustomParking_Env(config):
    """
    用于环境注册表的工厂函数

    Args:
        config: 环境配置参数

    Returns:
        CustomParkingEnv: 自定义停车环境实例
    """
    return CustomParkingEnv(config)
