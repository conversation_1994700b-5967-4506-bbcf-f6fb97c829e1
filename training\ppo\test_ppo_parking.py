#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ============================================================================
# 在导入任何其他模块之前先设置警告过滤
# ============================================================================
import warnings
import os

# 禁用所有警告
warnings.filterwarnings('ignore')

# 特别是禁用 gymnasium 的警告
os.environ['GYMNASIUM_DISABLE_WARNINGS'] = 'True'

"""
PPO停车环境测试脚本

该脚本用于测试已训练好的PPO停车智能体的性能。
支持加载训练好的模型，进行多回合测试，并生成详细的测试报告。
"""

import argparse
import numpy as np
import json
import time
from datetime import datetime
from copy import deepcopy
from pathlib import Path
from algorithms.utils import get_configs, recursive_dict_update
from algorithms.environments import make_envs
from algorithms.agents.utils.operations import set_seed
from algorithms.agents.agents import PPOCLIP_Agent


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser("PPO停车环境测试程序")
    parser.add_argument("config_file", nargs='?', default="ppo_configs/test_ppo_parking.yaml",
                       help="配置文件路径（可选，默认使用test_ppo_parking.yaml）")
    parser.add_argument("--env-id", type=str, default="parking-v0",
                       help="环境ID")
    parser.add_argument("--model-path", type=str, default=None,
                       help="模型文件路径（可选，如果不指定则使用配置文件中的路径）")
    parser.add_argument("--test-episodes", type=int, default=None,
                       help="测试回合数（可选，如果不指定则使用配置文件中的设置）")
    parser.add_argument("--render", type=int, default=None,
                       help="是否渲染 (0: 否, 1: 是，如果不指定则使用配置文件中的设置)")
    parser.add_argument("--save-results", type=int, default=1,
                       help="是否保存测试结果 (0: 否, 1: 是)")
    parser.add_argument("--verbose", type=int, default=1,
                       help="是否显示详细信息 (0: 否, 1: 是)")

    return parser.parse_args()


def create_directories(config):
    """创建必要的目录"""
    directories = [
        config.log_dir,
        config.model_dir,
        getattr(config, 'test_results_dir', './result/test/')
    ]

    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)


def convert_to_serializable(obj):
    """将numpy类型转换为Python原生类型，用于JSON序列化"""
    if isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.bool_):
        return bool(obj)
    elif isinstance(obj, dict):
        return {key: convert_to_serializable(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_to_serializable(item) for item in obj]
    else:
        return obj


def save_test_results(results, config, args):
    """保存测试结果到文件"""
    if not getattr(args, 'save_results', True):
        return

    # 创建结果目录
    results_dir = Path(getattr(config, 'test_results_dir', './result/test/results/'))
    results_dir.mkdir(parents=True, exist_ok=True)

    # 生成时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 转换结果为可序列化格式
    serializable_results = convert_to_serializable(results)

    # 保存详细结果
    results_file = results_dir / f"test_results_{timestamp}.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(serializable_results, f, indent=2, ensure_ascii=False)

    # 保存简要统计
    summary_file = results_dir / f"test_summary_{timestamp}.txt"
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write("PPO停车环境测试结果摘要\n")
        f.write("=" * 50 + "\n")
        f.write(f"测试时间: {results['test_info']['timestamp']}\n")
        f.write(f"模型路径: {results['test_info']['model_path']}\n")
        f.write(f"测试回合数: {results['test_info']['total_episodes']}\n")
        f.write(f"环境ID: {results['test_info']['env_id']}\n")
        f.write("\n性能统计:\n")
        f.write("-" * 30 + "\n")
        f.write(f"平均分数: {results['statistics']['mean_score']:.4f}\n")
        f.write(f"标准差: {results['statistics']['std_score']:.4f}\n")
        f.write(f"最高分数: {results['statistics']['max_score']:.4f}\n")
        f.write(f"最低分数: {results['statistics']['min_score']:.4f}\n")
        f.write(f"成功率: {results['statistics']['success_rate']:.2%}\n")
        f.write(f"平均回合长度: {results['statistics']['mean_episode_length']:.2f}\n")

        # 添加失败原因统计
        if 'failure_analysis' in results:
            f.write("\n失败原因分析:\n")
            f.write("-" * 20 + "\n")
            for reason, count in results['failure_analysis'].items():
                percentage = (count / results['test_info']['total_episodes']) * 100
                f.write(f"{reason}: {count}次 ({percentage:.1f}%)\n")

    print(f"测试结果已保存到: {results_file}")
    print(f"测试摘要已保存到: {summary_file}")


def analyze_episode_performance(episode_scores, episode_lengths, episode_details=None, success_threshold=0.0):
    """分析回合性能"""
    scores = np.array(episode_scores)
    lengths = np.array(episode_lengths)

    # 计算成功率（假设分数大于阈值为成功）
    success_count = np.sum(scores > success_threshold)
    success_rate = success_count / len(scores)

    statistics = {
        'mean_score': float(np.mean(scores)),
        'std_score': float(np.std(scores)),
        'max_score': float(np.max(scores)),
        'min_score': float(np.min(scores)),
        'median_score': float(np.median(scores)),
        'success_rate': float(success_rate),
        'success_count': int(success_count),
        'mean_episode_length': float(np.mean(lengths)),
        'std_episode_length': float(np.std(lengths)),
        'max_episode_length': int(np.max(lengths)),
        'min_episode_length': int(np.min(lengths))
    }

    # 添加速度统计信息
    if episode_details:
        max_speeds = [detail.get('max_speed', 0.0) for detail in episode_details]
        avg_speeds = [detail.get('avg_speed', 0.0) for detail in episode_details]
        final_speeds = [detail.get('final_speed', 0.0) for detail in episode_details]

        if max_speeds:
            statistics.update({
                'mean_max_speed': float(np.mean(max_speeds)),
                'max_max_speed': float(np.max(max_speeds)),
                'mean_avg_speed': float(np.mean(avg_speeds)),
                'mean_final_speed': float(np.mean(final_speeds)),
                'std_final_speed': float(np.std(final_speeds))
            })

    return statistics


def load_model_safely(agent, model_path):
    """
    安全地加载模型，绕过obs_rms.npy的路径问题
    """
    import torch

    # 规范化路径
    model_path = os.path.normpath(model_path)

    # 查找模型文件
    model_files = []
    for file in os.listdir(model_path):
        if file.endswith('.pth') or file.endswith('.pt'):
            model_files.append(file)

    if not model_files:
        raise RuntimeError(f"在路径 '{model_path}' 中没有找到模型文件！")

    # 选择最新的模型文件（通常是best_model.pth）
    model_files.sort()
    model_file = model_files[-1]  # 选择最后一个（按字母排序）

    # 如果有best_model.pth，优先选择它
    if 'best_model.pth' in model_files:
        model_file = 'best_model.pth'

    model_file_path = os.path.join(model_path, model_file)
    print(f"加载模型文件: {model_file_path}")

    # 直接加载PyTorch模型参数
    try:
        state_dict = torch.load(model_file_path, map_location=agent.config.device)
        agent.policy.load_state_dict(state_dict)
        print(f"Successfully load model from '{model_path}'.")
    except Exception as e:
        raise RuntimeError(f"加载模型参数失败: {e}")

    # 如果启用了观察归一化，尝试加载obs_rms.npy（可选）
    if agent.use_obsnorm:
        obs_rms_path = os.path.join(model_path, "obs_rms.npy")
        if os.path.exists(obs_rms_path):
            try:
                # 使用绝对路径来避免路径问题
                abs_obs_rms_path = os.path.abspath(obs_rms_path)
                observation_stat = np.load(abs_obs_rms_path, allow_pickle=True).item()
                agent.obs_rms.count = observation_stat['count']
                agent.obs_rms.mean = observation_stat['mean']
                agent.obs_rms.var = observation_stat['var']
                pass  # 观察归一化统计信息加载成功
            except Exception as e:
                pass  # 无法加载观察归一化文件，使用默认值
        else:
            pass  # 未找到观察归一化文件，使用默认值


def run_test_episodes(agent, env_fn, num_episodes, verbose=True):
    """运行测试回合并收集结果"""
    episode_scores = []
    episode_lengths = []
    episode_details = []

    print(f"开始运行 {num_episodes} 个测试回合...")

    for episode in range(num_episodes):
        if verbose:
            print(f"回合 {episode + 1}/{num_episodes}")

        # 重置环境
        envs = env_fn()
        obs, _ = envs.reset()

        episode_reward = 0
        episode_length = 0
        done = False
        truncated = False

        # 速度信息收集
        speeds = []  # 记录每步的速度
        max_speed = 0.0  # 最大速度
        final_speed = 0.0  # 最终速度

        # 运行单个回合
        while not (done or truncated):
            # 使用智能体选择动作（不需要探索，使用确定性策略）
            policy_out = agent.action(agent._process_observation(obs))
            action = policy_out['actions']

            # 执行动作
            next_obs, reward, done, truncated, info = envs.step(action)

            episode_reward += reward[0]  # 假设单环境
            episode_length += 1

            # 收集速度信息
            try:
                # 从环境中获取车辆速度信息
                if hasattr(envs, 'envs') and len(envs.envs) > 0:
                    env = envs.envs[0]  # 获取第一个环境
                    if hasattr(env, 'controlled_vehicles') and len(env.controlled_vehicles) > 0:
                        vehicle = env.controlled_vehicles[0]
                        current_speed = np.linalg.norm(vehicle.velocity)
                        speeds.append(current_speed)
                        max_speed = max(max_speed, current_speed)
                        final_speed = current_speed  # 更新最终速度
            except:
                pass  # 如果获取速度失败，忽略

            obs = next_obs

            # 防止无限循环
            if episode_length >= agent.config.max_episode_steps:
                truncated = True

        # 记录回合结果
        episode_scores.append(episode_reward)
        episode_lengths.append(episode_length)

        # 使用环境提供的真正成功判断
        env_success = info[0].get('is_success', False) if info else False

        # 分析失败原因：撞墙还是超时
        failure_reason = None
        if not env_success:
            if episode_length >= agent.config.max_episode_steps:
                failure_reason = "超时"
            elif info and info[0].get('crashed', False):
                failure_reason = "撞墙"
            else:
                failure_reason = "撞墙"  # 其他失败情况默认为撞墙

        # 计算速度统计信息
        avg_speed = np.mean(speeds) if speeds else 0.0

        episode_detail = {
            'episode': episode + 1,
            'score': float(episode_reward),
            'length': int(episode_length),
            'success': bool(env_success),
            'failure_reason': failure_reason if not env_success else None,
            'max_speed': float(max_speed),
            'avg_speed': float(avg_speed),
            'final_speed': float(final_speed),
            'info': info[0] if info else {}
        }
        episode_details.append(episode_detail)

        if verbose:
            if env_success:
                success_status = "成功"
                print(f"  分数: {episode_reward:.4f}, 已用步数: {episode_length}, 状态: {success_status}")
            else:
                success_status = f"失败({failure_reason})"
                print(f"  分数: {episode_reward:.4f}, 已用步数: {episode_length}, 状态: {success_status}")

        # 清理环境
        envs.close()

    return episode_scores, episode_lengths, episode_details


if __name__ == "__main__":
    # ============================================================================
    # 主程序入口
    # ============================================================================

    print("PPO停车环境测试程序")
    print("=" * 50)

    # 解析命令行参数
    args = parse_args()

    # 加载配置文件
    config_file = args.config_file
    if not config_file.startswith("ppo_configs/") and not os.path.isabs(config_file):
        config_file = f"ppo_configs/{config_file}"


    configs_dict = get_configs(file_dir=config_file)
    # 用命令行参数更新配置
    configs_dict = recursive_dict_update(configs_dict, args.__dict__)
    configs = argparse.Namespace(**configs_dict)

    # 应用命令行参数覆盖
    if args.test_episodes is not None:
        configs.test_episode = args.test_episodes
    if args.render is not None:
        configs.render = bool(args.render)
    if args.model_path is not None:
        configs.model_dir_load = args.model_path

    # 创建必要的目录
    create_directories(configs)

    # 设置随机种子
    if configs.seed is None:
        # 如果种子为None，使用当前时间生成随机种子
        import time
        random_seed = int(time.time() * 1000) % 100000
        set_seed(random_seed)
    else:
        set_seed(configs.seed)

    # 创建环境和智能体
    envs = make_envs(configs)
    agent = PPOCLIP_Agent(config=configs, envs=envs)

    # 打印测试信息
    test_information = {
        "深度学习框架": configs.dl_toolbox,
        "计算设备": configs.device,
        "算法": configs.agent,
        "环境名称": configs.env_name,
        "环境ID": configs.env_id,
        "测试回合数": configs.test_episode,
        "渲染模式": configs.render_mode if configs.render else "无渲染"
    }

    print("测试配置:")
    print("-" * 30)
    for k, v in test_information.items():
        print(f"{k}: {v}")

    # ============================================================================
    # 加载训练好的模型
    # ============================================================================

    model_path = getattr(configs, 'model_dir_load', './result/train/models/')
    model_name = getattr(configs, 'model_name_load', 'best_model.pth')

    # 规范化路径，处理路径分隔符
    model_path = os.path.normpath(model_path)
    full_model_path = os.path.join(model_path, model_name)

    print(f"加载模型: {full_model_path}")

    # 检查模型目录是否存在
    if not os.path.exists(model_path):
        print(f"错误: 模型目录不存在: {model_path}")
        print("请确保已完成训练并且模型路径正确。")
        exit(1)

    # 检查模型文件是否存在
    if not os.path.exists(full_model_path):
        print(f"错误: 模型文件不存在: {full_model_path}")
        print("请确保已完成训练并且模型文件路径正确。")
        # 列出目录中的文件以帮助调试
        print(f"目录 {model_path} 中的文件:")
        try:
            for file in os.listdir(model_path):
                print(f"  - {file}")
        except Exception as e:
            print(f"  无法列出目录内容: {e}")
        exit(1)

    try:
        agent.load_model(path=model_path)
        print("模型加载成功！")
        agent.policy.eval()
        print("模型已设置为评估模式")
    except Exception as e:
        print(f"模型加载失败: {e}")
        print(f"尝试加载的路径: {model_path}")
        exit(1)

    # ============================================================================
    # 执行测试
    # ============================================================================

    print("开始测试...")
    print("=" * 50)

    # 定义测试环境函数
    def env_fn():
        test_configs = deepcopy(configs)
        test_configs.parallels = 1  # 测试时使用单个环境
        return make_envs(test_configs)

    # 记录测试开始时间
    test_start_time = time.time()

    # 运行测试回合
    episode_scores, episode_lengths, episode_details = run_test_episodes(
        agent=agent,
        env_fn=env_fn,
        num_episodes=configs.test_episode,
        verbose=args.verbose
    )

    # 计算测试用时
    test_duration = time.time() - test_start_time

    # ============================================================================
    # 分析和显示结果
    # ============================================================================

    print(f"测试完成！用时: {test_duration:.2f} 秒")
    print("=" * 50)

    # 计算统计信息
    statistics = analyze_episode_performance(episode_scores, episode_lengths, episode_details)

    # 显示每回合详细结果
    print("回合详细结果:")
    print("-" * 30)
    success_count = 0
    failure_reasons = {}

    for detail in episode_details:
        if detail['success']:
            status = "成功"
            success_count += 1
        else:
            reason = detail.get('failure_reason', '未知')
            status = f"失败({reason})"
            failure_reasons[reason] = failure_reasons.get(reason, 0) + 1

        # 获取速度信息
        max_speed = detail.get('max_speed', 0.0)
        avg_speed = detail.get('avg_speed', 0.0)
        final_speed = detail.get('final_speed', 0.0)

        print(f"回合 {detail['episode']}: 分数 {detail['score']:.4f}, 已用步数 {detail['length']}, 状态 {status}")
        print(f"  速度信息: 最大 {max_speed:.3f}m/s, 平均 {avg_speed:.3f}m/s, 最终 {final_speed:.3f}m/s")

    print(f"总计: 成功 {success_count}/{len(episode_details)} 回合")

    # 显示失败原因统计
    if failure_reasons:
        print("\n失败原因统计:")
        print("-" * 20)
        for reason, count in failure_reasons.items():
            percentage = (count / len(episode_details)) * 100
            print(f"  {reason}: {count}次 ({percentage:.1f}%)")

    # 显示速度统计信息
    if 'mean_max_speed' in statistics:
        print("\n速度统计信息:")
        print("-" * 20)
        print(f"  平均最大速度: {statistics['mean_max_speed']:.3f} m/s")
        print(f"  全局最大速度: {statistics['max_max_speed']:.3f} m/s")
        print(f"  平均行驶速度: {statistics['mean_avg_speed']:.3f} m/s")
        print(f"  平均最终速度: {statistics['mean_final_speed']:.3f} m/s")
        print(f"  最终速度标准差: {statistics['std_final_speed']:.3f} m/s")

    # ============================================================================
    # 保存测试结果
    # ============================================================================

    if args.save_results:
        # 准备完整的结果数据
        results = {
            'test_info': {
                'timestamp': datetime.now().isoformat(),
                'model_path': full_model_path,
                'total_episodes': len(episode_scores),
                'test_duration': test_duration,
                'env_id': configs.env_id,
                'env_name': configs.env_name,
                'agent': configs.agent,
                'seed': configs.seed
            },
            'statistics': statistics,
            'failure_analysis': failure_reasons,
            'episode_details': episode_details,
            'config': {
                'max_episode_steps': configs.max_episode_steps,
                'render': configs.render,
                'render_mode': configs.render_mode,
                'device': configs.device
            }
        }

        # 保存结果
        save_test_results(results, configs, args)

    # 清理资源
    agent.finish()
    print("测试程序结束。")